# UI 偏好与布局
- 响应默认使用中文，用户使用Windows 11环境，偏好精英气息、精致界面、考究布局的商务风格
- 在app/新增功能时，应优先考虑使用app/uni_modules/下的模块，以提高对不同环境的适配性
- 用户偏好扁平化风格，不使用卡片式布局，减少边框和阴影的使用
- 用户偏好更加紧凑的界面布局，操作按钮放置在界面上方区域
- 用户偏好不使用下拉刷新、搜索功能、状态统计、卡片式设计、加载更多功能，列表项之间使用短横线分割的设计风格
- 用户偏好将section-title以标签形式显示在右侧，任务名称超长时使用省略号显示
- 将loading-container封装为组件，放置在app/components目录下，添加图标旋转动画效果
- 在Element Plus中，使用value替代已弃用的label作为el-checkbox的值
- 用户偏好即时搜索功能，不需要点击确定按钮即可实时显示搜索结果
- 用户偏好使用ECharts进行数据可视化展示，要求界面精致且具有商务感
- 在app/下添加新界面之后，需要检查app/pages.json中是否存在该界面

# 数据管理与API交互
- 登录后需请求getDictData方法，将字典数据保存到store中，参考app/pages/index.vue的解析方式
- 字典数据从接口获取而非硬编码或本地配置
- 获取userId使用this.$store.state.user.userId而非storage
- 日期格式应使用yyyy-MM-dd HH:mm:ss而非ISO格式
- 表单保存完成后返回列表界面时，应刷新列表数据显示最新状态
- 存储图片URL时只存储相对路径，不包含服务器地址前缀
- 在Vue组件中，不能在prop上直接使用v-model，应该使用v-bind绑定和v-on监听器发出update:x事件的组合方式
- 用户偏好发现时间字段只需精确到日期，不需要时分秒

# 站点与设备管理
- 在Equipment实体中添加inspectionRule字段存储巡检项配置，使用JSON格式

# 巡检与清洗系统
- 巡检任务状态值定义：0已下发 2已接收 4已提交 6已审核
- 清洗任务状态值定义：0已下发 2已接收 4已提交 6已审核
- 设备巡检项应直接从设备的inspectionRule字段获取，不从配置文件获取
- 巡检记录格式包含content、type、options、result、remark、photos字段
- radio类型检查项选项统一为'合格/不合格'，选择非第一个选项时必须填写备注
- 每个巡检项目有独立拍照上传区域，所有项目必须上传图片
- 在巡检表单上传图片时，文件名应重命名为格式：XJ_{设备名称}_{时间戳，如20250512105630}
- 在清洗表单中，负责人员选择使用UserSelect组件支持多选，计划时间使用时间段选择器且非必填项
- 在cleaning表中删除了user_id字段，添加了assign_user（多个ID用逗号分割）、assign_charge_user（单个ID）和cleaning_user字段
- 在清洗任务接收页面中，负责人的复选框应设置为不可取消选中状态
- 清洗任务完成判断依据：before_image、after_image、cleaning_method、cleaning_dust_thickness四个字段都必须填写完整
- 在清洗详情页面中，用户偏好使用更少空间展示cleaning-details区域，清洗前图片和清洗后图片各占一行的布局方式
- 用户偏好精简清洗详情界面，去除清洗状态统计和设备清洗详情中的设备类型、清洗人员字段显示

# 故障检修
- 故障状态值定义：0待确认 2已驳回 4已确认 6已完成
- 故障维修任务状态值定义：0已下发 2已接收 4已提交 6已审核
- 故障检修功能流程：巡检/清洗发现故障→记录故障信息→回传管理人员→管理人员判断维修方案→下达维修任务→手机端故障上传功能→实施人员接收任务→现场维修更换设备→记录维修位置和更换器件
- 故障检修功能包含Fault和FaultRepair两个实体，业务流程为：管理人员查看故障制定维修方案→下达维修任务给实施人员→实施人员接收任务并记录维修过程，需要完整的增删改查和任务管理功能
- 用户偏好在故障管理中点击创建按钮时直接调用FaultRepairForm组件，而不是使用对话框形式

# 电量统计
- 电量统计任务状态值定义：0已下发 2已接收 4已提交 6已审核


# 图片处理
- 用户要求在上传图片时添加水印功能，显示任务名称、设备名称、时间信息
- 在uni-app canvas水印实现中：使用动态canvas创建/销毁与显示控制，从图像信息设置适当的canvas尺寸，使用$nextTick创建canvas元素，在绘制操作前包含setTimeout，使用正确的drawImage参数进行图像渲染

# 工作台与个人页面
- 优化工作台任务类型导航区域，参考钉钉app工作台设计风格
- 将任务统计信息区域和巡检任务列表封装为组件提高复用性
- 表格中将开始和结束时间合并为"计划时间"列
- 个人页面工作历史区域使用巡检任务、组件清洗、故障检修、电量统计四栏目
- 用户偏好在个人页面添加管理后台入口，仅对具有admin或manage权限的用户显示，通过webview展示后台管理界面
- 用户偏好webview页面支持横向显示模式

# 部署与登录
- 用户偏好使用nginx进行生产部署
- 用户服务器环境为Rocky Linux 8.10，Intel i5-6300U CPU，7.4GB内存，70GB根分区，适合中小型应用部署
- 登录页面应移除默认账号密码，用户退出登录后再次登录时应预填写上次登录的账号密码

# 系统常量
- 系统名称为：E1030新能源智慧运维系统