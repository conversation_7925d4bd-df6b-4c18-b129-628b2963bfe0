<template>
  <div class="user-select-container">
    <div v-if="showSearch" class="user-select-search">
      <el-input
        v-model="searchValue"
        placeholder="搜索用户"
        clearable
        @input="handleSearch"
        prefix-icon="Search"
      >
        <template #append>
          <el-button @click="handleSearch">
            <el-icon><Search /></el-icon>
          </el-button>
        </template>
      </el-input>
    </div>

    <div class="user-select-content" v-loading="loading">
      <template v-if="mode === 'checkbox'">
        <el-checkbox-group v-model="selectedValues" @change="handleSelectionChange" :disabled="disabled">
          <el-scrollbar :height="height">
            <div class="user-checkbox-container">
              <el-checkbox
                v-for="user in filteredUserList"
                :key="user[valueKey]"
                :value="user[valueKey]"
                :disabled="disabled || user.disabled"
              >
                <div class="user-item">
                  <span>{{ getUserLabel(user) }}</span>
                </div>
              </el-checkbox>
            </div>
          </el-scrollbar>
        </el-checkbox-group>
      </template>

      <template v-else-if="mode === 'radio'">
        <el-radio-group v-model="selectedValue" @change="handleSingleSelectionChange" :disabled="disabled">
          <el-scrollbar :height="height">
            <div class="user-radio-container">
              <el-radio
                v-for="user in filteredUserList"
                :key="user[valueKey]"
                :label="user[valueKey]"
                :disabled="disabled || user.disabled"
              >
                <div class="user-item">
                  <span>{{ getUserLabel(user) }}</span>
                </div>
              </el-radio>
            </div>
          </el-scrollbar>
        </el-radio-group>
      </template>

      <template v-else-if="mode === 'select'">
        <el-select
          v-if="multiple"
          v-model="selectedValues"
          multiple
          :placeholder="placeholder"
          style="width: 100%"
          @change="handleSelectionChange"
          filterable
          :filter-method="filterSelectOptions"
          :disabled="disabled"
        >
          <el-option
            v-for="user in userList"
            :key="user[valueKey]"
            :label="getUserLabel(user)"
            :value="user[valueKey]"
            :disabled="disabled || user.disabled"
          >
            <div class="user-item">
              <span>{{ getUserLabel(user) }}</span>
            </div>
          </el-option>
        </el-select>
        <el-select
          v-else
          v-model="selectedValue"
          :placeholder="placeholder"
          style="width: 100%"
          @change="handleSingleSelectionChange"
          filterable
          :filter-method="filterSelectOptions"
          :disabled="disabled"
        >
          <el-option
            v-for="user in userList"
            :key="user[valueKey]"
            :label="getUserLabel(user)"
            :value="user[valueKey]"
            :disabled="disabled || user.disabled"
          >
            <div class="user-item">
              <span>{{ getUserLabel(user) }}</span>
            </div>
          </el-option>
        </el-select>
      </template>
    </div>

    <div v-if="showPagination && total > pageSize" class="user-select-pagination">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        layout="prev, pager, next"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { listUser } from '@/api/system/user.js';
import { Search } from '@element-plus/icons-vue';

const props = defineProps({
  // 选择模式：checkbox(复选框), radio(单选框), select(下拉选择)
  mode: {
    type: String,
    default: 'checkbox',
    validator: (value) => ['checkbox', 'radio', 'select'].includes(value)
  },
  // 是否多选（仅在 mode=select 时有效）
  multiple: {
    type: Boolean,
    default: true
  },
  // 默认选中的值（单选时为单个值，多选时为数组）
  modelValue: {
    type: [Array, Number, String],
    default: () => []
  },
  // 值的键名
  valueKey: {
    type: String,
    default: 'userId'
  },
  // 标签的键名或格式化函数
  labelKey: {
    type: [String, Function],
    default: 'nickName'
  },
  // 描述的键名（用于显示额外信息）
  descriptionKey: {
    type: String,
    default: 'userName'
  },
  // 是否显示搜索框
  showSearch: {
    type: Boolean,
    default: true
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 高度
  height: {
    type: String,
    default: '200px'
  },
  // 占位符文本
  placeholder: {
    type: String,
    default: '请选择用户'
  },
  // 查询条件
  queryParams: {
    type: Object,
    default: () => ({})
  },
  // 是否显示分页
  showPagination: {
    type: Boolean,
    default: false
  },
  // 每页显示数量
  pageSize: {
    type: Number,
    default: 10
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

// 响应式数据
const userList = ref([]);
const loading = ref(false);
const searchValue = ref('');
const selectedValues = ref(Array.isArray(props.modelValue) ? [...props.modelValue] : []);
const selectedValue = ref(!Array.isArray(props.modelValue) ? props.modelValue : null);
const currentPage = ref(1);
const total = ref(0);

// 计算属性：根据搜索值过滤用户列表
const filteredUserList = computed(() => {
  if (!searchValue.value) {
    return userList.value;
  }

  const searchLower = searchValue.value.toLowerCase();
  return userList.value.filter((user) => {
    const nameMatch = typeof user.nickName === 'string' && user.nickName.toLowerCase().includes(searchLower);
    const userNameMatch =
      typeof user.userName === 'string' && user.userName.toLowerCase().includes(searchLower);
    return nameMatch || userNameMatch;
  });
});

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (Array.isArray(newVal)) {
      selectedValues.value = [...newVal];
    } else {
      selectedValue.value = newVal;
    }
  },
  { deep: true }
);

// 获取用户列表
const loadUserList = async () => {
  loading.value = true;
  try {
    const queryParams = {
      pageNum: props.showPagination ? currentPage.value : 1,
      pageSize: props.showPagination ? props.pageSize : 999,
      ...props.queryParams
    };

    const response = await listUser(queryParams);
    userList.value = response.rows || [];
    total.value = response.total || 0;
  } catch (error) {
    console.error('Failed to load user list:', error);
  } finally {
    loading.value = false;
  }
};

// 处理多选变化
const handleSelectionChange = (value) => {
  emit('update:modelValue', value);
  emit('change', value);
};

// 处理单选变化
const handleSingleSelectionChange = (value) => {
  emit('update:modelValue', value);
  emit('change', value);
};

// 处理搜索
const handleSearch = () => {
  if (props.showPagination) {
    currentPage.value = 1;
    loadUserList();
  }
};

// 处理分页变化
const handlePageChange = (page) => {
  currentPage.value = page;
  loadUserList();
};

// 过滤下拉选择框选项
const filterSelectOptions = (query) => {
  searchValue.value = query;
  return true; // 返回true，让计算属性filteredUserList处理过滤
};

// 获取用户标签显示
const getUserLabel = (user) => {
  if (typeof props.labelKey === 'function') {
    return props.labelKey(user);
  }

  if (props.descriptionKey && user[props.descriptionKey]) {
    return `${user[props.labelKey] || ''} (${user[props.descriptionKey] || ''})`;
  }

  return user[props.labelKey] || '';
};

// 组件挂载时加载用户列表
onMounted(() => {
  loadUserList();
});
</script>

<style scoped>
.user-select-container {
  width: 100%;
}

.user-select-search {
  margin-bottom: 10px;
}

.user-checkbox-container,
.user-radio-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 5px;
}

.user-item {
  display: flex;
  align-items: center;
}

.user-select-pagination {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}
</style>
