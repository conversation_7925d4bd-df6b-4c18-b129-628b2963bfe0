<template>
  <el-cascader
    v-model="selectedRegion"
    :options="regionData"
    :props="props"
    :placeholder="placeholder"
    :disabled="disabled"
    :clearable="clearable"
    :size="size"
    @change="handleChange"
  />
</template>

<script setup>
import { ref, watch, defineProps, defineEmits } from 'vue';
import { regionData, codeToText } from 'element-china-area-data';

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  placeholder: {
    type: String,
    default: '请选择地区'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  size: {
    type: String,
    default: 'default'
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

const selectedRegion = ref(props.modelValue || []);
const cascaderProps = {
  expandTrigger: 'hover'
};

// 监听外部传入的值变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal !== selectedRegion.value) {
      selectedRegion.value = newVal;
    }
  },
  { deep: true }
);

// 监听内部选择的值变化
watch(
  () => selectedRegion.value,
  (newVal) => {
    emit('update:modelValue', newVal);
  },
  { deep: true }
);

// 处理选择变化
const handleChange = (value) => {
  // 获取选中地区的文本表示, 处理value为空的情况
  if (!value) {
    emit('change', {
      codes: [],
      texts: []
    });
    return;
  }
  const regionTexts = value.map((code) => codeToText[code]);
  emit('change', {
    codes: value,
    texts: regionTexts
  });
};
</script>
