-- ----------------------------
-- 1、部门表
-- ----------------------------
drop table if exists sys_dept;
create table sys_dept
(
    dept_id     bigint(20) not null auto_increment comment '部门id',
    parent_id   bigint(20)  default 0 comment '父部门id',
    ancestors   varchar(50) default '' comment '祖级列表',
    dept_name   varchar(30) default '' comment '部门名称',
    order_num   int(4)      default 0 comment '显示顺序',
    leader      varchar(20) default null comment '负责人',
    phone       varchar(11) default null comment '联系电话',
    email       varchar(50) default null comment '邮箱',
    status      char(1)     default '0' comment '部门状态（0正常 1停用）',
    del_flag    char(1)     default '0' comment '删除标志（0代表存在 2代表删除）',
    create_by   varchar(64) default '' comment '创建者',
    create_time datetime comment '创建时间',
    update_by   varchar(64) default '' comment '更新者',
    update_time datetime comment '更新时间',
    primary key (dept_id)
) engine = innodb
  auto_increment = 200 comment = '部门表';


-- ----------------------------
-- 2、用户信息表
-- ----------------------------
drop table if exists sys_user;
create table sys_user
(
    user_id     bigint(20)  not null auto_increment comment '用户ID',
    dept_id     bigint(20)   default null comment '部门ID',
    user_name   varchar(30) not null comment '用户账号',
    nick_name   varchar(30) not null comment '用户昵称',
    user_type   varchar(2)   default '00' comment '用户类型（00系统用户）',
    email       varchar(50)  default '' comment '用户邮箱',
    phonenumber varchar(11)  default '' comment '手机号码',
    sex         char(1)      default '0' comment '用户性别（0男 1女 2未知）',
    avatar      varchar(100) default '' comment '头像地址',
    password    varchar(100) default '' comment '密码',
    status      char(1)      default '0' comment '帐号状态（0正常 1停用）',
    del_flag    char(1)      default '0' comment '删除标志（0代表存在 2代表删除）',
    login_ip    varchar(128) default '' comment '最后登录IP',
    login_date  datetime comment '最后登录时间',
    create_by   varchar(64)  default '' comment '创建者',
    create_time datetime comment '创建时间',
    update_by   varchar(64)  default '' comment '更新者',
    update_time datetime comment '更新时间',
    remark      varchar(500) default null comment '备注',
    primary key (user_id)
) engine = innodb
  auto_increment = 100 comment = '用户信息表';


-- ----------------------------
-- 3、岗位信息表
-- ----------------------------
drop table if exists sys_post;
create table sys_post
(
    post_id     bigint(20)  not null auto_increment comment '岗位ID',
    post_code   varchar(64) not null comment '岗位编码',
    post_name   varchar(50) not null comment '岗位名称',
    post_sort   int(4)      not null comment '显示顺序',
    status      char(1)     not null comment '状态（0正常 1停用）',
    create_by   varchar(64)  default '' comment '创建者',
    create_time datetime comment '创建时间',
    update_by   varchar(64)  default '' comment '更新者',
    update_time datetime comment '更新时间',
    remark      varchar(500) default null comment '备注',
    primary key (post_id)
) engine = innodb comment = '岗位信息表';


-- ----------------------------
-- 4、角色信息表
-- ----------------------------
drop table if exists sys_role;
create table sys_role
(
    role_id             bigint(20)   not null auto_increment comment '角色ID',
    role_name           varchar(30)  not null comment '角色名称',
    role_key            varchar(100) not null comment '角色权限字符串',
    role_sort           int(4)       not null comment '显示顺序',
    data_scope          char(1)      default '1' comment '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
    menu_check_strictly tinyint(1)   default 1 comment '菜单树选择项是否关联显示',
    dept_check_strictly tinyint(1)   default 1 comment '部门树选择项是否关联显示',
    status              char(1)      not null comment '角色状态（0正常 1停用）',
    del_flag            char(1)      default '0' comment '删除标志（0代表存在 2代表删除）',
    create_by           varchar(64)  default '' comment '创建者',
    create_time         datetime comment '创建时间',
    update_by           varchar(64)  default '' comment '更新者',
    update_time         datetime comment '更新时间',
    remark              varchar(500) default null comment '备注',
    primary key (role_id)
) engine = innodb
  auto_increment = 100 comment = '角色信息表';


-- ----------------------------
-- 5、菜单权限表
-- ----------------------------
drop table if exists sys_menu;
create table sys_menu
(
    menu_id     bigint(20)  not null auto_increment comment '菜单ID',
    menu_name   varchar(50) not null comment '菜单名称',
    parent_id   bigint(20)   default 0 comment '父菜单ID',
    order_num   int(4)       default 0 comment '显示顺序',
    path        varchar(200) default '' comment '路由地址',
    component   varchar(255) default null comment '组件路径',
    query       varchar(255) default null comment '路由参数',
    route_name  varchar(50)  default '' comment '路由名称',
    is_frame    int(1)       default 1 comment '是否为外链（0是 1否）',
    is_cache    int(1)       default 0 comment '是否缓存（0缓存 1不缓存）',
    menu_type   char(1)      default '' comment '菜单类型（M目录 C菜单 F按钮）',
    visible     char(1)      default 0 comment '菜单状态（0显示 1隐藏）',
    status      char(1)      default 0 comment '菜单状态（0正常 1停用）',
    perms       varchar(100) default null comment '权限标识',
    icon        varchar(100) default '#' comment '菜单图标',
    create_by   varchar(64)  default '' comment '创建者',
    create_time datetime comment '创建时间',
    update_by   varchar(64)  default '' comment '更新者',
    update_time datetime comment '更新时间',
    remark      varchar(500) default '' comment '备注',
    primary key (menu_id)
) engine = innodb
  auto_increment = 2000 comment = '菜单权限表';


-- ----------------------------
-- 6、用户和角色关联表  用户N-1角色
-- ----------------------------
drop table if exists sys_user_role;
create table sys_user_role
(
    user_id bigint(20) not null comment '用户ID',
    role_id bigint(20) not null comment '角色ID',
    primary key (user_id, role_id)
) engine = innodb comment = '用户和角色关联表';


-- ----------------------------
-- 7、角色和菜单关联表  角色1-N菜单
-- ----------------------------
drop table if exists sys_role_menu;
create table sys_role_menu
(
    role_id bigint(20) not null comment '角色ID',
    menu_id bigint(20) not null comment '菜单ID',
    primary key (role_id, menu_id)
) engine = innodb comment = '角色和菜单关联表';


-- ----------------------------
-- 8、角色和部门关联表  角色1-N部门
-- ----------------------------
drop table if exists sys_role_dept;
create table sys_role_dept
(
    role_id bigint(20) not null comment '角色ID',
    dept_id bigint(20) not null comment '部门ID',
    primary key (role_id, dept_id)
) engine = innodb comment = '角色和部门关联表';


-- ----------------------------
-- 9、用户与岗位关联表  用户1-N岗位
-- ----------------------------
drop table if exists sys_user_post;
create table sys_user_post
(
    user_id bigint(20) not null comment '用户ID',
    post_id bigint(20) not null comment '岗位ID',
    primary key (user_id, post_id)
) engine = innodb comment = '用户与岗位关联表';


-- ----------------------------
-- 电站信息表
-- ----------------------------
drop table if exists station;
create table station
(
    station_id   bigint(20)    not null auto_increment comment '电站ID',
    station_name varchar(1000) not null comment '电站名称',
    owner_type   varchar(20)   not null comment '投资类型',
    station_type varchar(20)   not null comment '电站类型',
    dc_capacity  decimal(10, 2) default 0 comment '直流侧容量(MW)',
    ac_capacity  decimal(10, 2) default 0 comment '交流侧容量(MW)',
    grid_time    datetime comment '并网时间',
    district     varchar(255)   default '' comment '地区信息数组',
    address      varchar(1000)  default '' comment '详细地址',
    images       varchar(2000)  default '' comment '电站照片',
    grid_type    varchar(20)    default '' comment '并网类型',
    price_type   varchar(2000)  default '' comment '电价计算方式',
    contact      varchar(2000)  default '' comment '用户联系方式',
    status       char(1)        default '0' comment '电站状态',
    del_flag     char(1)        default '0' comment '删除标志',
    create_by    varchar(64)    default '' comment '创建者',
    create_time  datetime comment '创建时间',
    update_by    varchar(64)    default '' comment '更新者',
    update_time  datetime comment '更新时间',
    remark       varchar(2000)  default null comment '备注',
    primary key (station_id)
) engine = innodb
  auto_increment = 100 comment = '电站信息表';


-- ----------------------------
-- 设备信息表
-- ----------------------------
drop table if exists equipment;
create table equipment
(
    equipment_id    bigint(20)   not null auto_increment comment '设备ID',
    station_id      bigint(20)   null comment '所属电站ID',
    equipment_code  varchar(200) null comment '设备编号',
    equipment_name  varchar(200) not null comment '设备名称',
    equipment_type  varchar(200) null comment '设备类型',
    manufacturer    varchar(200) null comment '生产厂家',
    model           varchar(200) null comment '设备型号',
    quantity        varchar(50)   default '' comment '设备数量',
    attachments     varchar(2000) default '' comment '附件路径',
    inspection_rule text         null comment '巡检规则（JSON格式）',
    status          char(1)       default '0' comment '设备状态（0正常 1停用）',
    del_flag        char(1)       default '0' comment '删除标志（0存在 2删除）',
    create_by       varchar(64)   default '' comment '创建者',
    create_time     datetime comment '创建时间',
    update_by       varchar(64)   default '' comment '更新者',
    update_time     datetime comment '更新时间',
    remark          varchar(2000) default null comment '备注',
    primary key (equipment_id)
) engine = innodb
  auto_increment = 100 comment = '设备信息表';


-- ----------------------------
-- 巡检任务表
-- ----------------------------
drop table if exists inspection;
create table inspection
(
    inspection_id      bigint(20)   not null auto_increment comment '巡检ID',
    station_id         bigint(20)   not null comment '电站ID',
    inspection_name    varchar(200) not null comment '巡检任务名称',
    assign_start_time  datetime     default null comment '设定开始时间',
    assign_end_time    datetime     default null comment '设定结束时间',
    assign_user        varchar(200) not null comment '设定巡检人员',
    assign_charge_user varchar(200) not null comment '设定负责人',
    inspector_user     varchar(200) default null comment '实际巡检人员',
    receive_time       datetime     default null comment '接收时间',
    submit_time        datetime     default null comment '提交时间',
    review_time        datetime     default null comment '审核时间',
    status             char(1)      default '0' comment '状态',
    del_flag           char(1)      default '0' comment '删除标志',
    create_by          varchar(64)  default '' comment '创建者',
    create_time        datetime comment '创建时间',
    update_by          varchar(64)  default '' comment '更新者',
    update_time        datetime comment '更新时间',
    remark             varchar(500) default null comment '备注',
    primary key (inspection_id)
) engine = innodb
  auto_increment = 100 comment = '巡检任务表';


-- ----------------------------
-- 巡检和设备关联表  巡检N-N设备
-- ----------------------------
drop table if exists inspection_equipment;
create table inspection_equipment
(
    inspection_id      bigint(20) not null comment '巡检ID',
    equipment_id       bigint(20) not null comment '设备ID',
    user_id            bigint(20)    default null comment '巡检人员ID',
    inspection_content text          default null comment '巡检内容',
    inspection_time    datetime comment '巡检时间',
    equipment_remark   varchar(2000) default null comment '检查备注',
    primary key (inspection_id, equipment_id)
) engine = innodb comment = '巡检和设备关联表';

-- ----------------------------
-- 组件清洗任务表
-- ----------------------------
drop table if exists cleaning;
create table cleaning
(
    cleaning_id        bigint(20)   NOT null auto_increment comment '清洗任务ID',
    cleaning_name      varchar(100) default null comment '清洗任务名称',
    station_id         bigint(20)   default null comment '电站ID',
    plan_start_time    datetime     default null comment '计划开始时间',
    plan_end_time      datetime     default null comment '计划结束时间',
    assign_user        varchar(200) not null comment '设定清洗人员',
    assign_charge_user varchar(200) not null comment '设定负责人',
    cleaning_user      varchar(200) default null comment '实际清洗人员',
    receive_time       datetime     default null comment '接收时间',
    submit_time        datetime     default null comment '提交时间',
    review_time        datetime     default null comment '审核时间',
    review_remark      varchar(500) default null comment '审核备注',
    status             char(1)      default '0' comment '状态',
    del_flag           char(1)      default '0' comment '删除标志',
    create_by          varchar(64)  default '' comment '创建者',
    create_time        datetime     default null comment '创建时间',
    update_by          varchar(64)  default '' comment '更新者',
    update_time        datetime     default null comment '更新时间',
    remark             varchar(500) default null comment '备注',
    PRIMARY KEY (cleaning_id)
) engine = innodb
  auto_increment = 100 comment = '组件清洗任务表';


-- ----------------------------
-- 组件清洗和设备关联表  清洗N-N设备
-- ----------------------------
drop table if exists cleaning_equipment;
create table cleaning_equipment
(
    cleaning_id             bigint(20) not null comment '清洗任务ID',
    equipment_id            bigint(20) not null comment '设备ID',
    user_id                 bigint(20)    default null comment '清洗人员ID',
    before_image            varchar(2000) default null comment '清洗前照片',
    after_image             varchar(2000) default null comment '清洗后照片',
    cleaning_method         char(1)       default null comment '清洗方式',
    cleaning_dust_thickness char(1)       default null comment '表面灰尘厚度',
    cleaning_time           datetime comment '清洗时间',
    cleaning_remark         varchar(2000) default null comment '清洗备注',
    primary key (cleaning_id, equipment_id)
) engine = innodb comment = '组件清洗和设备关联表';

-- ----------------------------
-- 故障信息表
-- ----------------------------
DROP TABLE IF EXISTS fault;
CREATE TABLE fault
(
    fault_id          BIGINT(20)   NOT NULL AUTO_INCREMENT COMMENT '故障ID',
    station_id        BIGINT(20)   NOT NULL COMMENT '电站ID',
    equipment_list_id VARCHAR(200) NULL COMMENT '设备ID列表',
    fault_name        VARCHAR(200) NOT NULL COMMENT '故障名称',
    fault_type        varchar(20)  NULL COMMENT '故障类型',
    fault_level       varchar(20)  NULL COMMENT '故障等级',
    fault_description TEXT          DEFAULT NULL COMMENT '故障描述',
    fault_images      VARCHAR(2000) DEFAULT NULL COMMENT '故障照片',
    fault_source      VARCHAR(50)   DEFAULT NULL COMMENT '故障来源',
    discover_user_id  BIGINT(20)    DEFAULT NULL COMMENT '发现人员ID',
    discover_time     DATETIME      DEFAULT NULL COMMENT '发现时间',
    status            CHAR(1)       DEFAULT '0' COMMENT '状态',
    review_user       VARCHAR(200)  DEFAULT NULL COMMENT '审核人员',
    review_time       DATETIME      DEFAULT NULL COMMENT '审核时间',
    review_comment    VARCHAR(500)  DEFAULT NULL COMMENT '审核意见',
    del_flag          CHAR(1)       DEFAULT '0' COMMENT '删除标志',
    create_by         VARCHAR(64)   DEFAULT '' COMMENT '创建者',
    create_time       DATETIME COMMENT '创建时间',
    update_by         VARCHAR(64)   DEFAULT '' COMMENT '更新者',
    update_time       DATETIME COMMENT '更新时间',
    remark            VARCHAR(500)  DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (fault_id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100 COMMENT ='故障信息表';

-- ----------------------------
-- 维修任务表
-- ----------------------------
DROP TABLE IF EXISTS fault_repair;
CREATE TABLE fault_repair
(
    repair_id          BIGINT(20)   NOT NULL AUTO_INCREMENT COMMENT '维修ID',
    fault_id           BIGINT(20)   NOT NULL COMMENT '故障ID',
    repair_name        VARCHAR(200) NOT NULL COMMENT '维修任务名称',
    assign_user        VARCHAR(200) NOT NULL COMMENT '指派维修人员',
    assign_charge_user VARCHAR(200) NOT NULL COMMENT '指派负责人',
    repair_user        VARCHAR(200)  DEFAULT NULL COMMENT '实际维修人员',
    plan_start_time    DATETIME      DEFAULT NULL COMMENT '计划开始时间',
    plan_end_time      DATETIME      DEFAULT NULL COMMENT '计划结束时间',
    receive_time       DATETIME      DEFAULT NULL COMMENT '接收时间',
    repair_plan        TEXT          DEFAULT NULL COMMENT '维修方案',
    repair_process     TEXT          DEFAULT NULL COMMENT '维修过程记录',
    replaced_parts     TEXT          DEFAULT NULL COMMENT '更换器件记录',
    before_images      VARCHAR(2000) DEFAULT NULL COMMENT '维修前照片',
    after_images       VARCHAR(2000) DEFAULT NULL COMMENT '维修后照片',
    repair_remark      VARCHAR(2000) DEFAULT NULL COMMENT '维修备注',
    submit_time        DATETIME      DEFAULT NULL COMMENT '提交时间',
    status             CHAR(1)       DEFAULT '0' COMMENT '状态',
    review_user        VARCHAR(200)  DEFAULT NULL COMMENT '审核人员',
    review_time        DATETIME      DEFAULT NULL COMMENT '审核时间',
    review_comment     VARCHAR(500)  DEFAULT NULL COMMENT '审核意见',
    del_flag           CHAR(1)       DEFAULT '0' COMMENT '删除标志',
    create_by          VARCHAR(64)   DEFAULT '' COMMENT '创建者',
    create_time        DATETIME COMMENT '创建时间',
    update_by          VARCHAR(64)   DEFAULT '' COMMENT '更新者',
    update_time        DATETIME COMMENT '更新时间',
    remark             VARCHAR(500)  DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (repair_id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100 COMMENT ='维修任务表';

-- ----------------------------
-- 电量统计任务表
-- ----------------------------
drop table if exists meter_reading;
create table meter_reading
(
    meter_reading_id   bigint(20)   NOT null auto_increment comment '电量统计任务ID',
    meter_reading_name varchar(100) default null comment '电量统计任务名称',
    station_id         bigint(20)   default null comment '电站ID',
    plan_start_time    datetime     default null comment '计划开始时间',
    plan_end_time      datetime     default null comment '计划结束时间',
    assign_user        varchar(200) not null comment '设定电量统计人员',
    assign_charge_user varchar(200) not null comment '设定负责人',
    meter_reading_user varchar(200) default null comment '实际电量统计人员',
    receive_time       datetime     default null comment '接收时间',
    submit_time        datetime     default null comment '提交时间',
    review_time        datetime     default null comment '审核时间',
    review_remark      varchar(500) default null comment '审核备注',
    status             char(1)      default '0' comment '状态',
    del_flag           char(1)      default '0' comment '删除标志',
    create_by          varchar(64)  default '' comment '创建者',
    create_time        datetime     default null comment '创建时间',
    update_by          varchar(64)  default '' comment '更新者',
    update_time        datetime     default null comment '更新时间',
    remark             varchar(500) default null comment '备注',
    PRIMARY KEY (meter_reading_id)
) engine = innodb
  auto_increment = 100 comment = '电量统计任务表';


-- ----------------------------
-- 电量统计和设备关联表  电量统计N-N设备
-- ----------------------------
drop table if exists meter_reading_equipment;
create table meter_reading_equipment
(
    meter_reading_id          bigint(20) not null comment '电量统计任务ID',
    equipment_id              bigint(20) not null comment '设备ID',
    user_id                   bigint(20)     default null comment '电量统计人员ID',
    last_month_reverse_active decimal(15, 4) default null comment '上一月反向有功',
    total_reverse_active      decimal(15, 4) default null comment '反向有功总',
    photos                    varchar(2000)  default null comment '照片',
    meter_reading_time        datetime comment '电量统计时间',
    meter_reading_remark      varchar(2000)  default null comment '电量统计备注',
    primary key (meter_reading_id, equipment_id)
) engine = innodb comment = '电量统计和设备关联表';
